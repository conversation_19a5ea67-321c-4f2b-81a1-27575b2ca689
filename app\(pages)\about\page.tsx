"use client";

import { motion } from "framer-motion";
import { Award, Users, Rocket, ArrowRight, CheckCircle2, MapPin, Calendar, Globe, Heart, Star, TrendingUp, Code, Lightbulb, Target } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { GradientButton } from "@/components/ui/gradient-button";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { PageHero } from "@/components/ui/page-hero";
import { Section } from "@/components/ui/section";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";

// Company Statistics
const stats = [
  { number: "50+", label: "Years Combined Experience", icon: Award },
  { number: "100+", label: "Projects Delivered", icon: Rocket },
  { number: "95%", label: "Client Satisfaction Rate", icon: Star },
  { number: "24/7", label: "Global Support", icon: Globe },
];

// Company Timeline
const timeline = [
  {
    year: "2022",
    title: "The College Project",
    description: "Started as a final-year project among friends at college in Silchar, Assam, with a vision to transform ideas into digital reality.",
    icon: Lightbulb,
  },
  {
    year: "2023",
    title: "Flerid Technologies Founded",
    description: "Officially established in Silchar, Assam, focusing on web development and digital marketing solutions for local businesses.",
    icon: Rocket,
  },
  {
    year: "2024",
    title: "First Local Success & Growth",
    description: "Delivered major projects for businesses across India, increasing their online presence by 300% and expanding our team nationwide.",
    icon: TrendingUp,
  },
  {
    year: "2025",
    title: "Global Expansion & Innovation",
    description: "Expanded services internationally, serving clients across India and globally while establishing ourselves as a leading tech company.",
    icon: Globe,
  },
  {
    year: "Future",
    title: "Innovation Hub Vision",
    description: "Continuing to grow as the premier web development company in Silchar, serving both local Assam businesses and international clients.",
    icon: Target,
  },
];

// Core Values & Achievements
const achievements = [
  {
    title: "Local Expertise, Global Impact",
    description: "Deep understanding of Silchar and Assam market needs combined with international best practices for scalable solutions.",
    icon: MapPin,
    highlight: "Silchar-based with global reach",
  },
  {
    title: "Innovation from Day One",
    description: "Born from a college project, we maintain our startup spirit while delivering enterprise-grade web development and digital marketing solutions.",
    icon: Lightbulb,
    highlight: "College project to tech company",
  },
  {
    title: "Client Success First",
    description: "50+ years of combined team experience focused on delivering measurable results for businesses from Silchar to international markets.",
    icon: Target,
    highlight: "Proven track record",
  },
];

// Team Members
const teamMembers = [
  {
    name: "Rahul Sharma",
    role: "Co-Founder & Lead Developer",
    experience: "15+ years",
    bio: "Expert in full-stack web development with extensive experience in React, Node.js, and cloud technologies. Leads our technical innovation.",
    image: "/team/rahul-placeholder.jpg", // Placeholder for team photo
    specialties: ["Web Development", "Cloud Solutions", "Technical Leadership"],
  },
  {
    name: "Priya Devi",
    role: "Co-Founder & Digital Marketing Director",
    experience: "12+ years",
    bio: "Digital marketing strategist specializing in SEO, social media, and growth hacking for both local Assam businesses and global brands.",
    image: "/team/priya-placeholder.jpg", // Placeholder for team photo
    specialties: ["Digital Marketing", "SEO", "Brand Strategy"],
  },
  {
    name: "Amit Kumar",
    role: "Senior UI/UX Designer",
    experience: "10+ years",
    bio: "Creative designer focused on user-centered design principles, creating intuitive interfaces for web and mobile applications.",
    image: "/team/amit-placeholder.jpg", // Placeholder for team photo
    specialties: ["UI/UX Design", "Mobile Design", "User Research"],
  },
  {
    name: "Sneha Choudhury",
    role: "Project Manager & Client Relations",
    experience: "8+ years",
    bio: "Ensures seamless project delivery and maintains strong relationships with clients from Silchar to international markets.",
    image: "/team/sneha-placeholder.jpg", // Placeholder for team photo
    specialties: ["Project Management", "Client Relations", "Quality Assurance"],
  },
];

// Services & Expertise with SEO-optimized descriptions
const expertise = [
  {
    service: "Custom Web Development",
    description: "Tailored web solutions for Silchar businesses and global clients using cutting-edge technologies",
    keywords: "web development Silchar, custom websites Assam",
  },
  {
    service: "Mobile App Development",
    description: "Native and cross-platform mobile applications for iOS and Android with global scalability",
    keywords: "mobile app development India, global app solutions",
  },
  {
    service: "UI/UX Design",
    description: "User-centered design creating intuitive interfaces that drive engagement and conversions",
    keywords: "UI UX design services, user experience Silchar",
  },
  {
    service: "Digital Marketing",
    description: "Comprehensive digital marketing strategies for local Assam businesses and international brands",
    keywords: "digital marketing agency India, SEO services Assam",
  },
  {
    service: "E-commerce Solutions",
    description: "Complete online store development with payment integration and inventory management",
    keywords: "ecommerce development Silchar, online store solutions",
  },
  {
    service: "Cloud Services",
    description: "Scalable cloud infrastructure and migration services for businesses of all sizes",
    keywords: "cloud services India, AWS Azure solutions",
  },
  {
    service: "SEO Optimization",
    description: "Local and global SEO strategies to improve search rankings and drive organic traffic",
    keywords: "SEO company Silchar, search engine optimization Assam",
  },
  {
    service: "Social Media Marketing",
    description: "Engaging social media campaigns that build brand awareness and drive customer acquisition",
    keywords: "social media marketing India, SMM services Assam",
  },
];

// Client Testimonials & Social Proof
const testimonials = [
  {
    client: "Local Restaurant Chain, Silchar",
    testimonial: "Flerid Technologies increased our online orders by 200% with their excellent web development and digital marketing services. Their understanding of the local Silchar market is exceptional.",
    result: "200% increase in online orders",
    industry: "Food & Beverage",
  },
  {
    client: "E-commerce Startup, Mumbai",
    testimonial: "Working with Flerid was a game-changer. Their team delivered a scalable e-commerce platform that helped us expand from local to national markets.",
    result: "Scaled from local to national",
    industry: "E-commerce",
  },
  {
    client: "Educational Institute, Assam",
    testimonial: "The mobile app developed by Flerid transformed how we connect with students. Their local expertise combined with technical excellence is unmatched.",
    result: "Enhanced student engagement",
    industry: "Education",
  },
];

// Company Metrics & Achievements
const companyMetrics = [
  "Increased local business website traffic by 50% with expert SEO",
  "Delivered e-commerce solutions for global brands",
  "95% client retention rate across Silchar and international projects",
  "24/7 support for clients worldwide",
  "ISO 9001:2015 Quality Management certified",
  "Google Partner certified for digital marketing",
];

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section - SEO Optimized */}
      <PageHero
        title="About Flerid Technologies | Web Development & Digital Marketing Company Silchar, India"
        description="Flerid Technologies transforms ideas into digital reality, delivering innovative web development and digital marketing solutions to empower businesses in Silchar, Assam, and worldwide. From a college project to a leading tech company."
        titleGradient="blue"
        badges={[
          { text: "Silchar, Assam, India Based", variant: "secondary" },
          { text: "Global Reach", variant: "outline" },
          { text: "50+ Years Combined Experience", variant: "outline" },
          { text: "College Project Origin", variant: "secondary" },
        ]}
      />

      {/* Mission Statement Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto text-center"
          >
            <GradientHeading
              size="lg"
              gradient="blue"
              className="mb-6"
              as="h2"
            >
              Our Mission: Bridging Local Expertise with Global Innovation
            </GradientHeading>
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              <strong>Flerid Technologies transforms ideas into digital reality</strong>, delivering innovative web development and digital marketing solutions to empower businesses in Silchar, Assam, and worldwide. As a leading <strong>web development company in Silchar</strong> and <strong>digital marketing agency in India</strong>, we bridge local market understanding with global technological excellence.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-12">
              {stats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-600 to-cyan-600 flex items-center justify-center">
                    <stat.icon className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600 mb-2">
                    {stat.number}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Company Story & Timeline Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <GradientHeading
              size="lg"
              gradient="purple"
              className="mb-6"
              as="h2"
            >
              From College Project to Global Tech Company
            </GradientHeading>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Our journey began in <strong>2022</strong> during the final year of college in <strong>Silchar, Assam</strong>, where a group of passionate friends working on their final-year project discovered their shared vision: to transform ideas into digital reality. What started as a student project just three years ago has rapidly evolved into a leading <strong>web development company in Silchar</strong> serving clients globally.
            </p>
          </motion.div>

          {/* Timeline */}
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-600 to-cyan-600 rounded-full"></div>

              {timeline.map((item, index) => (
                <motion.div
                  key={item.year}
                  initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.2 }}
                  className={`relative flex items-center mb-12 ${
                    index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                  }`}
                >
                  {/* Timeline dot */}
                  <div className="absolute left-1/2 transform -translate-x-1/2 w-12 h-12 bg-gradient-to-br from-blue-600 to-cyan-600 rounded-full flex items-center justify-center z-10">
                    <item.icon className="h-6 w-6 text-white" />
                  </div>

                  {/* Content */}
                  <div className={`w-5/12 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                    <Card className="hover:shadow-lg transition-all duration-300">
                      <CardContent className="p-6">
                        <Badge variant="secondary" className="mb-3">
                          {item.year}
                        </Badge>
                        <h3 className="text-xl font-semibold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600">
                          {item.title}
                        </h3>
                        <p className="text-muted-foreground">
                          {item.description}
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Flerid Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <GradientHeading
              size="lg"
              gradient="blue"
              className="mb-4"
              as="h2"
            >
              Why Choose Flerid Technologies
            </GradientHeading>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Local expertise meets global innovation - delivering exceptional web development and digital marketing results for businesses in Silchar, Assam, and worldwide
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-600 to-cyan-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <achievement.icon className="h-6 w-6 text-white" />
                    </div>
                    <Badge variant="outline" className="mb-3 text-xs">
                      {achievement.highlight}
                    </Badge>
                    <GradientHeading
                      size="sm"
                      gradient="blue"
                      className="mb-3"
                      as="h3"
                    >
                      {achievement.title}
                    </GradientHeading>
                    <p className="text-muted-foreground leading-relaxed">
                      {achievement.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <GradientHeading
              size="lg"
              gradient="green"
              className="mb-6"
              as="h2"
            >
              Meet Our Expert Team
            </GradientHeading>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              <strong>50+ years of combined experience</strong> serving clients from Silchar to global markets. Our diverse team of experts brings together local market knowledge and international best practices.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={member.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-6 text-center">
                    {/* Placeholder for team member photo */}
                    <div className="w-24 h-24 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-600 to-cyan-600 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                      <Users className="h-12 w-12 text-white" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600">
                      {member.name}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-2">
                      {member.role}
                    </p>
                    <Badge variant="secondary" className="mb-4">
                      {member.experience}
                    </Badge>
                    <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
                      {member.bio}
                    </p>
                    <div className="flex flex-wrap gap-1 justify-center">
                      {member.specialties.map((specialty) => (
                        <Badge key={specialty} variant="outline" className="text-xs">
                          {specialty}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services & Expertise Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <GradientHeading
              size="lg"
              gradient="green"
              className="mb-4"
              as="h2"
            >
              Our Services & Expertise
            </GradientHeading>
            <p className="text-muted-foreground max-w-3xl mx-auto">
              Comprehensive <strong>web development</strong> and <strong>digital marketing solutions</strong> tailored for businesses in <strong>Silchar, Assam</strong> and global markets
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {expertise.map((item, index) => {
              // Map service names to their corresponding service IDs
              const serviceSlugMap: Record<string, string> = {
                'Custom Web Development': 'web-development',
                'Mobile App Development': 'app-development',
                'UI/UX Design': 'web-development', // UI/UX is part of web development
                'Digital Marketing': 'social-media-marketing',
                'E-commerce Solutions': 'web-development', // E-commerce is part of web development
                'Cloud Services': 'software-development',
                'SEO Optimization': 'seo',
                'Social Media Marketing': 'social-media-marketing',
              };

              const serviceSlug = serviceSlugMap[item.service] || 'web-development';

              return (
                <motion.div
                  key={item.service}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link href={`/services/${serviceSlug}`} className="block h-full">
                    <Card className="h-full hover:shadow-lg transition-all duration-300 group cursor-pointer">
                      <CardContent className="p-6">
                        <div className="flex items-start gap-3 mb-4">
                          <CheckCircle2 className="h-6 w-6 text-blue-600 mt-1 group-hover:scale-110 transition-transform duration-300" />
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600 group-hover:from-blue-700 group-hover:to-cyan-700 transition-all duration-300">
                              {item.service}
                            </h3>
                            <p className="text-muted-foreground text-sm mb-3 leading-relaxed">
                              {item.description}
                            </p>
                            <div className="flex items-center justify-between">
                              <Badge variant="outline" className="text-xs">
                                {item.keywords}
                              </Badge>
                              <ArrowRight className="h-4 w-4 text-blue-600 group-hover:translate-x-1 transition-transform duration-300" />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Client Testimonials & Social Proof Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <GradientHeading
              size="lg"
              gradient="purple"
              className="mb-6"
              as="h2"
            >
              Client Success Stories
            </GradientHeading>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              From local businesses in <strong>Silchar, Assam</strong> to international brands, our clients achieve measurable results with our web development and digital marketing expertise.
            </p>
          </motion.div>

          {/* Testimonials */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={testimonial.client}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-4">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <p className="text-muted-foreground mb-4 italic leading-relaxed">
                      "{testimonial.testimonial}"
                    </p>
                    <div className="border-t pt-4">
                      <p className="font-semibold text-sm mb-1">
                        {testimonial.client}
                      </p>
                      <Badge variant="secondary" className="text-xs mb-2">
                        {testimonial.industry}
                      </Badge>
                      <p className="text-sm text-blue-600 font-medium">
                        Result: {testimonial.result}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Company Metrics */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="bg-gradient-to-r from-blue-600 to-cyan-600 rounded-2xl p-8 text-white"
          >
            <h3 className="text-2xl font-bold text-center mb-8">
              Proven Track Record
            </h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {companyMetrics.map((metric, index) => (
                <motion.div
                  key={metric}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center gap-3"
                >
                  <CheckCircle2 className="h-5 w-5 text-white flex-shrink-0" />
                  <span className="text-sm">{metric}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-muted/50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center max-w-4xl mx-auto"
          >
            <GradientHeading
              size="lg"
              gradient="blue"
              className="mb-6"
              as="h2"
            >
              Grow Your Business Locally or Globally – Contact Us Today
            </GradientHeading>
            <p className="text-lg text-muted-foreground mb-8 leading-relaxed">
              Ready to transform your ideas into digital reality? Whether you're a local business in <strong>Silchar, Assam</strong> or a global enterprise, our expert team is here to deliver innovative <strong>web development</strong> and <strong>digital marketing solutions</strong> that drive results.
            </p>

            {/* Contact Information */}
            <div className="grid md:grid-cols-3 gap-6 mb-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-600 to-cyan-600 flex items-center justify-center">
                  <MapPin className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold mb-2">Location</h3>
                <p className="text-muted-foreground text-sm">
                  Silchar, Assam, India<br />
                  Serving Local & Global Markets
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2 }}
                className="text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-600 to-cyan-600 flex items-center justify-center">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold mb-2">Email</h3>
                <p className="text-muted-foreground text-sm">
                  <EMAIL><br />
                  24/7 Response Guarantee
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.3 }}
                className="text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-blue-600 to-cyan-600 flex items-center justify-center">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <h3 className="font-semibold mb-2">Phone</h3>
                <p className="text-muted-foreground text-sm">
                  +91 6003351943<br />
                  +91 7002135973<br />
                  +91 6901527553
                </p>
              </motion.div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                asChild
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white shadow-lg font-medium px-8"
                style={{
                  textShadow: '0 1px 2px rgba(0, 0, 0, 0.7)'
                }}
              >
                <Link
                  href="/contact"
                  className="service-button-text"
                >
                  Start Your Project Today
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="px-8"
              >
                <Link href="/#services">
                  Explore Our Services
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Additional SEO Content */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.4 }}
              className="mt-12 p-6 bg-background rounded-lg border"
            >
              <p className="text-sm text-muted-foreground leading-relaxed">
                <strong>Flerid Technologies</strong> - Your trusted <strong>web development company in Silchar, Assam</strong> and <strong>digital marketing agency in India</strong>. From our humble beginnings as a college project in 2022 to becoming a leading technology partner in just three years, we specialize in custom web development, mobile app development, SEO services, and comprehensive digital marketing solutions for businesses in Silchar, across Assam, throughout India, and globally. Contact us today to transform your ideas into digital reality.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
