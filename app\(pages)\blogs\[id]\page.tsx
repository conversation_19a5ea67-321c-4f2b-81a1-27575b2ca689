"use client";

import { useEffect, useState } from "react";
import React from "react";
import { ArrowLeft, Calendar, Clock, Share2, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";
import { motion } from "framer-motion";
import { cn, getAppwriteImageUrl } from "@/lib/utils";
import { fetchBlogById } from "@/lib/appwrite";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import TurndownService from "turndown";

const turndownService = new TurndownService({
  headingStyle: "atx",
  codeBlock: true,
  bulletListMarker: "-",
  blankReplacement: function(content, node) {
    return node.isBlock ? '\n\n' : '';
  }
});

// Configure heading rules
turndownService.addRule("paragraphs", {
  filter: "p",
  replacement: function(content) {
    return '\n\n' + content + '\n\n';
  }
});

turndownService.addRule("headings", {
  filter: ["h1", "h2", "h3", "h4", "h5", "h6"],
  replacement: function (content, node) {
    return '\n\n**' + content + '**\n\n';
  },
});

export default function BlogPost({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const resolvedParams = React.use(params);
  const [blog, setBlog] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);


  useEffect(() => {
    fetchBlogData();
  }, [resolvedParams.id]);

  const fetchBlogData = async () => {
    try {
      const data = await fetchBlogById(resolvedParams.id);
      setBlog(data);
    } catch (error) {
      console.error("Error fetching blog:", error);
      setError("Failed to load blog post");
    } finally {
      setIsLoading(false);
    }
  };

  const convertHtmlToMarkdown = (html: string) => {
    let markdown = turndownService.turndown(html);

    // Ensure proper line breaks between paragraphs
    markdown = markdown.replace(/\n\s*\n/g, '\n\n');
    
    // Remove any triple or more line breaks
    markdown = markdown.replace(/\n{3,}/g, '\n\n');
    
    // Remove escape characters
    markdown = markdown.replace(/\\/g, '');

    return markdown.trim();
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: blog.title,
        url: window.location.href,
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
        <Loader2 className="w-8 h-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error || !blog) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center px-4"
        >
          <h1 className="text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600">
            {error || "Blog Post Not Found"}
          </h1>
          <p className="text-muted-foreground mb-6">
            The article you're looking for doesn't exist or has been removed.
          </p>
          <Link href="/blogs">
            <Button variant="outline" size="lg">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Articles
            </Button>
          </Link>
        </motion.div>
      </div>
    );
  }

  const readingTime = Math.ceil(blog.content.split(" ").length / 200); // Assuming 200 words per minute

  return (
    <article className="min-h-screen bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
      {/* Hero Section */}
      <div className="relative">
        {blog.featuredImage && (
          <div className="relative w-full h-[60vh] min-h-[400px]">
            <Image
              src={
                getAppwriteImageUrl(blog.featuredImage) ||
                "/placeholder-blog.jpg"
              }
              alt={blog.title}
              fill
              className="object-cover"
              priority
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = "/placeholder-blog.jpg";
                target.onerror = null;
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background via-background/50 to-transparent" />
          </div>
        )}

        <div className="container mx-auto px-4">
          <div className="relative py-12 max-w-4xl mx-auto">
            <Link href="/blogs">
              <Button
                variant="ghost"
                className="mb-8 hover:bg-background/80 backdrop-blur-sm"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Articles
              </Button>
            </Link>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                {blog.title}
              </h1>

              <div className="flex flex-wrap items-center gap-6 text-muted-foreground mb-8">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4" />
                  <time dateTime={blog.$createdAt}>
                    {new Date(blog.$createdAt).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </time>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  <span>{readingTime} min read</span>
                </div>
                {navigator.share && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="ml-auto"
                    onClick={handleShare}
                  >
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </Button>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="container mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            className={cn(
              "prose prose-lg dark:prose-invert max-w-none",
              "prose-headings:scroll-mt-20",
              "prose-a:text-primary prose-a:no-underline hover:prose-a:underline",
              "prose-img:rounded-lg prose-img:shadow-lg",
              "prose-pre:bg-muted prose-pre:shadow-sm",
              "prose-blockquote:border-l-primary prose-blockquote:bg-muted/50 prose-blockquote:px-6 prose-blockquote:py-4 prose-blockquote:rounded-r-lg"
            )}
          >
            {convertHtmlToMarkdown(blog.content)}
          </ReactMarkdown>

          {/* Footer */}
          <div className="mt-12 pt-6 border-t">
            <Button
              variant="outline"
              size="lg"
              className="w-full sm:w-auto"
              asChild
            >
              <Link href="/blogs">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Articles
              </Link>
            </Button>
          </div>
        </motion.div>
      </div>
    </article>
  );
}
