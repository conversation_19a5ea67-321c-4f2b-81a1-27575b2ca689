"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MapPin, Phone, Mail, Send } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { submitContactForm } from "@/lib/appwrite";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { cn } from "@/lib/utils";

const formSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  company: z.string().optional(),
  message: z.string().min(10, "Message must be at least 10 characters"),
});

const contactInfo = {
  address: {
    line1: "Silchar",
    line2: "Assam",
    line3: "India",
  },
  phones: [
    { number: "+91 6003351943" },
    { number: "+91 7002135973" },
    { number: "+91 6901527553" },
  ],
  email: "<EMAIL>",
  hours: [
    { days: "Monday - Friday", time: "9:00 AM - 6:00 PM IST" },
    { days: "Saturday", time: "9:00 AM - 1:00 PM IST" },
    { days: "Sunday", time: "Closed" },
  ],
};

export default function ContactPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      company: "",
      message: "",
    },
  });

  async function onSubmit(data: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true);
      await submitContactForm(data);

      toast.success("Message sent successfully!", {
        description: "We'll get back to you soon.",
      });

      form.reset();
    } catch (error) {
      toast.error("Failed to send message", {
        description: "Please try again later.",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute -top-1/2 -right-1/2 w-[800px] h-[800px] rounded-full bg-blue-500/10 blur-3xl" />
          <div className="absolute -bottom-1/2 -left-1/2 w-[800px] h-[800px] rounded-full bg-cyan-500/10 blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <GradientHeading
              size="xl"
              gradient="blue"
              className="mb-6"
              as="h1"
            >
              Get in Touch
            </GradientHeading>
            <p className="text-lg text-muted-foreground">
              Have a question or want to work together? We&apos;d love to hear
              from you.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Contact Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5 }}
              className="lg:col-span-1 space-y-6"
            >
              {/* Address Card */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-start gap-4">
                  <div className="rounded-full p-3 bg-blue-100 dark:bg-blue-900/30 text-blue-600">
                    <MapPin className="w-6 h-6" />
                  </div>
                  <div>
                    <GradientHeading
                      size="sm"
                      gradient="blue"
                      className="mb-2"
                      as="h3"
                    >
                      Visit Us
                    </GradientHeading>
                    <p className="text-muted-foreground">
                      {contactInfo.address.line1}
                      <br />
                      {contactInfo.address.line2}
                      <br />
                      {contactInfo.address.line3}
                    </p>
                  </div>
                </div>
              </div>

              {/* Phone Card */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-start gap-4">
                  <div className="rounded-full p-3 bg-blue-100 dark:bg-blue-900/30 text-blue-600">
                    <Phone className="w-6 h-6" />
                  </div>
                  <div>
                    <GradientHeading
                      size="sm"
                      gradient="green"
                      className="mb-2"
                      as="h3"
                    >
                      Call Us
                    </GradientHeading>
                    {contactInfo.phones.map((phone, index) => (
                      <p key={index} className="text-muted-foreground">
                        {phone.number} {phone.flag}
                      </p>
                    ))}
                  </div>
                </div>
              </div>

              {/* Email Card */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-start gap-4">
                  <div className="rounded-full p-3 bg-blue-100 dark:bg-blue-900/30 text-blue-600">
                    <Mail className="w-6 h-6" />
                  </div>
                  <div>
                    <GradientHeading
                      size="sm"
                      gradient="purple"
                      className="mb-2"
                      as="h3"
                    >
                      Email Us
                    </GradientHeading>
                    <a
                      href={`mailto:${contactInfo.email}`}
                      className="text-blue-600 hover:text-blue-700 transition-colors"
                    >
                      {contactInfo.email}
                    </a>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="lg:col-span-2"
            >
              <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg">
                <GradientHeading
                  size="md"
                  gradient="purple"
                  className="mb-6"
                  as="h2"
                >
                  Send us a Message
                </GradientHeading>

                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6 z-50"
                  >
                    <div className="grid md:grid-cols-2 gap-6 z-500">
                      <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>First Name</FormLabel>
                            <FormControl>
                              <Input placeholder="John" {...field} className="border-blue-600" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Last Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Doe" {...field} className="border-blue-600" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                      <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email</FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                {...field}
                                className="border-blue-600"
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input placeholder="+1234567890" {...field} className="border-blue-600" />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="company"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Company (Optional)</FormLabel>
                          <FormControl>
                            <Input placeholder="Your Company" {...field} className="border-blue-600" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Message</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Tell us about your project..."
                              className="min-h-[150px] resize-none border-blue-600"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className={cn(
                        "w-full h-12",
                        "bg-gradient-to-r from-blue-600 to-cyan-600",
                        "hover:from-blue-700 hover:to-cyan-700",
                        "text-white",
                        "flex items-center justify-center gap-2"
                      )}
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          <span>Sending...</span>
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4" />
                          <span>Send Message</span>
                        </>
                      )}
                    </Button>
                  </form>
                </Form>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
