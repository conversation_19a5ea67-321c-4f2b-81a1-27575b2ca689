"use client";

import { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Link from "next/link";
import { ExternalLink, Loader2 } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Meteors } from "@/components/ui/meteors";
import { PlaceholderImage } from "@/components/ui/placeholder-image";
import { cn } from "@/lib/utils";
import { GradientHeading } from "@/components/ui/gradient-heading";
import { PortfolioItem, fetchPortfolioItems, getPortfolioImageUrl } from "@/lib/appwrite";

// Gradient colors for different categories using Flerid's color palette
const gradientMap: Record<string, string> = {
  "Web Development": "from-navy to-teal",
  "Mobile App": "from-teal to-navy",
  "UI/UX Design": "from-navy to-warm-gray",
  "E-commerce": "from-teal to-warm-gray",
  "Default": "from-navy to-teal",
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: "easeOut",
    },
  }),
};

export default function PortfolioPage() {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPortfolio = async () => {
      try {
        // Fetch portfolio items - sorted by creation date (newest first)
        const items = await fetchPortfolioItems();
        setPortfolioItems(items);
      } catch (error) {
        console.error("Error fetching portfolio items:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPortfolio();
  }, []);

  // Function to determine the grid class based on index
  const getGridClass = (index: number) => {
    // Make cards smaller and more consistent
    if (index % 4 === 0) {
      return "md:col-span-1";
    } else if (index % 3 === 0) {
      return "md:col-span-1";
    } else {
      return "md:col-span-1";
    }
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        {/* Background gradients */}
        <div className="absolute inset-0 z-0">
          <div className="absolute -top-1/2 -right-1/2 w-[800px] h-[800px] rounded-full bg-teal/10 blur-3xl" />
          <div className="absolute -bottom-1/2 -left-1/2 w-[800px] h-[800px] rounded-full bg-navy/10 blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center max-w-3xl mx-auto"
          >
            <GradientHeading
              size="xl"
              gradient="purple"
              className="mb-6"
              as="h1"
            >
              Our Portfolio
            </GradientHeading>
            <p className="text-lg dark:text-warm-gray text-navy/70 mb-8">
              Explore some of our recent projects and see how we've helped businesses achieve their digital goals
            </p>
          </motion.div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <Loader2 className="w-8 h-8 animate-spin text-primary" />
            </div>
          ) : portfolioItems.length === 0 ? (
            <div className="text-center py-10">
              <p className="text-muted-foreground">No portfolio items found. Check back soon!</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
              {portfolioItems.map((project, index) => (
                <motion.div
                  key={project.$id}
                  custom={index}
                  initial="hidden"
                  whileInView="visible"
                  viewport={{ once: true }}
                  variants={cardVariants}
                  className={getGridClass(index)}
                >
                  <Card
                    className={cn(
                      `group relative overflow-hidden border-2 border-warm-gray/30 hover:border-teal transition-colors h-full transition-all duration-300 shadow-md hover:shadow-lg hover:shadow-teal/10 bg-soft-white dark:bg-navy/80`
                    )}
                  >
                    {/* Project Image */}
                    <div className="relative h-56 w-full overflow-hidden">
                      <PlaceholderImage
                        src={project.image ? getPortfolioImageUrl(project.image) : "/placeholder-project.svg"}
                        alt={project.title}
                        fill
                        className="object-cover object-center transition-transform duration-500 group-hover:scale-105"
                      />
                      <div
                        className={cn(
                          "absolute inset-0 opacity-40 bg-gradient-to-t",
                          gradientMap[project.category] || gradientMap["Default"]
                        )}
                      />
                    </div>

                    <CardHeader className="p-4 pb-2">
                      <CardTitle className="text-lg mb-1 dark:text-soft-white text-navy">
                        {project.title}
                      </CardTitle>
                      <CardDescription className="text-sm dark:text-warm-gray text-navy/70 line-clamp-2">
                        {project.description}
                      </CardDescription>
                    </CardHeader>

                    <CardContent className="z-10 p-4 pt-0">
                      {/* Features */}
                      {project.features && project.features.length > 0 && (
                        <div className="mb-4">
                          <h4 className="text-xs font-semibold uppercase tracking-wider text-navy dark:text-soft-white/70 mb-2">Key Features</h4>
                          <div className="flex flex-wrap gap-1.5 mb-2">
                            {project.features.map((feature, idx) => {
                              // Generate a consistent color based on the feature title
                              const featureColors = [
                                "bg-teal/20 dark:bg-teal/40 text-teal-800 dark:text-teal-100 border-teal/30",
                                "bg-navy/10 dark:bg-navy/40 text-navy dark:text-navy-100 border-navy/30",
                                "bg-blue-100 dark:bg-blue-900/40 text-blue-800 dark:text-blue-100 border-blue-200",
                                "bg-purple-100 dark:bg-purple-900/40 text-purple-800 dark:text-purple-100 border-purple-200",
                                "bg-indigo-100 dark:bg-indigo-900/40 text-indigo-800 dark:text-indigo-100 border-indigo-200",
                                "bg-emerald-100 dark:bg-emerald-900/40 text-emerald-800 dark:text-emerald-100 border-emerald-200",
                                "bg-rose-100 dark:bg-rose-900/40 text-rose-800 dark:text-rose-100 border-rose-200",
                              ];
                              const colorIndex = Math.abs(feature.title.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % featureColors.length;

                              return (
                                <div
                                  key={idx}
                                  className={`${featureColors[colorIndex]} px-2.5 py-1 rounded-md text-xs border flex items-center gap-1.5`}
                                  title={feature.description}
                                >
                                  <span className="font-medium">{feature.title}</span>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )}

                      {/* Technologies */}
                      <div className="mb-4">
                        <h4 className="text-xs font-semibold uppercase tracking-wider text-navy dark:text-soft-white/70 mb-2">Technologies</h4>
                        <div className="flex flex-wrap gap-1.5">
                          {project.technologies.map((tech) => {
                            // Generate a consistent color based on the technology name
                            const colors = [
                              "bg-navy/10 dark:bg-navy/30 text-navy dark:text-soft-white",
                              "bg-teal/10 dark:bg-teal/30 text-teal-800 dark:text-teal-200",
                              "bg-warm-gray/20 dark:bg-warm-gray/30 text-gray-700 dark:text-warm-gray",
                              "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200",
                              "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200",
                              "bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-200",
                              "bg-emerald-100 dark:bg-emerald-900/30 text-emerald-800 dark:text-emerald-200",
                            ];
                            const colorIndex = Math.abs(tech.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % colors.length;

                            return (
                              <Badge
                                key={tech}
                                variant="secondary"
                                className={`${colors[colorIndex]} text-xs px-2 py-0.5 rounded-md`}
                              >
                                {tech}
                              </Badge>
                            );
                          })}
                        </div>
                      </div>

                      {/* View Project Button - Conditionally rendered based on projectUrl */}
                      {project.projectUrl ? (
                        <Button
                          variant="default"
                          className="group/button bg-teal hover:bg-teal/90 text-soft-white w-full"
                          asChild
                        >
                          <Link href={project.projectUrl}>
                            View Project
                            <ExternalLink className="ml-2 h-4 w-4 transition-transform group-hover/button:translate-x-1" />
                          </Link>
                        </Button>
                      ) : (
                        <Button
                          variant="outline"
                          className="group/button text-navy dark:text-warm-gray w-full cursor-not-allowed opacity-60"
                          disabled
                        >
                          Coming Soon
                        </Button>
                      )}
                    </CardContent>

                    <Meteors number={10} />
                  </Card>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>
    </div>
  );
}