"use client";
import React, { useEffect, useState } from "react";
import { Sidebar, SidebarBody, SidebarLink } from "./ui/sidebar";

import {
  BookOpen,
  DollarSign,
  HomeIcon,
  Mail,
  Moon,
  Sun,
  Users,
  Briefcase,
  Wrench,
} from "lucide-react"; // Import Moon and Sun icons
import Link from "next/link";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button"; // Import Button component
import Image from "next/image";

export function AppSidebar() {
  const links = [
    {
      label: "Home",
      href: "/",
      icon: <HomeIcon className="h-5 w-5 flex-shrink-0" />,
      color: "text-gray-800 dark:text-white",
    },
    {
      label: "About Us",
      href: "/about",
      icon: <Users className="h-5 w-5 flex-shrink-0" />,
      color: "text-blue-600 dark:text-blue-300",
    },
    {
      label: "Services",
      href: "/services",
      icon: <Wrench className="h-5 w-5 flex-shrink-0" />,
      color: "text-blue-600 dark:text-blue-300",
    },
    {
      label: "Portfolio",
      href: "/portfolio",
      icon: <Briefcase className="h-5 w-5 flex-shrink-0" />,
      color: "text-green-600 dark:text-green-300",
    },
    {
      label: "Pricing",
      href: "/pricing",
      icon: <DollarSign className="h-5 w-5 flex-shrink-0" />,
      color: "text-green-600 dark:text-green-300",
    },
   
    {
      label: "Blogs",
      href: "/blogs",
      icon: <BookOpen className="h-5 w-5 flex-shrink-0" />,
      color: "text-purple-600 dark:text-purple-300",
    },
    {
      label: "Contact Us",
      href: "/contact",
      icon: <Mail className="h-5 w-5 flex-shrink-0" />,
      color: "text-purple-600 dark:text-purple-300",
    },
  ];

  const [open, setOpen] = useState(false);

  return (
    <Sidebar open={open} setOpen={setOpen}>
      <SidebarBody className="justify-between gap-10">
        <div className="flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
          {open ? <Logo /> : <LogoIcon />}
          <div className="mt-8 flex flex-col gap-2">
            {links.map((link, idx) => (
              <SidebarLink key={idx} link={link} />
            ))}
            <div className="mt-8 flex">
              <ThemeToggle />
            </div>
          </div>
        </div>
      </SidebarBody>
    </Sidebar>
  );
}

export const Logo = () => {
  return (
    <Link
      href="#"
      className="font-normal flex space-x-2 items-center text-sm text-black py-1 relative z-20"
    >
      <Image src="/logo.png" alt="Flerid Technologies" width={24} height={24} />
      <motion.span
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="font-medium text-black dark:text-white whitespace-pre"
      >
        Flerid Technologies
      </motion.span>
    </Link>
  );
};

export const LogoIcon = () => {
  return (
    <Link
      href="#"
      className="font-normal flex space-x-2 items-center text-sm text-black py-1 relative z-20"
    >
      <Image src="/logo.png" alt="Flerid Technologies" width={24} height={24} />
    </Link>
  );
};

// Theme Toggle Component
const ThemeToggle = () => {
  const [theme, setTheme] = useState<"light" | "dark">("light");

  useEffect(() => {
    const isDark = document.documentElement.classList.contains("dark");
    setTheme(isDark ? "dark" : "light");
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme);
    document.documentElement.classList.toggle("dark");
  };

  return (
    <Button variant="ghost" size="icon" onClick={toggleTheme}>
      {theme === "light" ? (
        <Moon className="h-5 w-5 text-blue-600" />
      ) : (
        <Sun className="h-5 w-5 text-blue-300" />
      )}
    </Button>
  );
};
