"use client"
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";

import { cn } from "@/lib/utils";
import { 
  Menu, 
  Home,
  Moon, 
  Sun,
  Users,
  DollarSign,
  Wrench,
  BookOpen,
  Mail
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";

const NavItems = [
  {
    title: "Home",
    href: "/",
    icon: <Home className="h-4 w-4" />,
  },
  {
    title: "About Us",
    href: "/about",
    icon: <Users className="h-4 w-4" />,
  },
  {
    title: "Pricing",
    href: "/pricing",
    icon: <DollarSign className="h-4 w-4" />,
  },
  {
    title: "Services",
    href: "/services",
    icon: <Wrench className="h-4 w-4" />,
  },
  {
    title: "Blogs",
    href: "/blogs",
    icon: <BookOpen className="h-4 w-4" />,
  },
  {
    title: "Contact Us",
    href: "/contact",
    icon: <Mail className="h-4 w-4" />,
  },
];

const ThemeToggle = () => {
  const [theme, setTheme] = useState<'light' | 'dark'>('light');

  useEffect(() => {
    const isDark = document.documentElement.classList.contains('dark');
    setTheme(isDark ? 'dark' : 'light');
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    document.documentElement.classList.toggle('dark');
  };

  return (
    <Button variant="ghost" size="icon" onClick={toggleTheme}>
      {theme === 'light' ? (
        <Moon className="h-5 w-5" />
      ) : (
        <Sun className="h-5 w-5" />
      )}
    </Button>
  );
};

const MobileNav = () => {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-6 w-6" />
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-72">
        <div className="flex flex-col">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold">Navigation Menu</h2>
          </div>
          <div className="flex flex-col gap-4 mt-6">
            {NavItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="flex items-center gap-2 px-4 py-2 text-sm rounded-md hover:bg-accent"
              >
                {item.icon}
                {item.title}
              </Link>
            ))}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <header
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-300",
        isScrolled ? "bg-background/80 backdrop-blur-md border-b" : "bg-background"
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo - Left */}
          <div className="flex-none">
            <Link href="/" className="flex items-center gap-2">
              <Image src="/logo.png" width={32} height={32} alt="Flerid Technologies" />
              <span className="font-bold text-lg">FLERID TECHNOLOGIES</span>
            </Link>
          </div>

          {/* Navigation - Center */}
          <nav className="hidden md:flex items-center gap-8">
            {NavItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="flex items-center gap-2 text-sm font-medium text-muted-foreground hover:text-primary transition-colors"
              >
                {item.icon}
                {item.title}
              </Link>
            ))}
          </nav>

          {/* Theme Toggle and Mobile Nav - Right */}
          <div className="flex items-center gap-2">
            <ThemeToggle />
            <MobileNav />
          </div>
        </div>
      </div>
    </header>
  );
}