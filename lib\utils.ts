import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getAppwriteImageUrl(fileId: string): string {
  if (!fileId || !process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT || !process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || !process.env.NEXT_PUBLIC_APPWRITE_BLOG_IMAGES_BUCKET_ID) {
    return '';
  }

  try {
    return `${process.env.NEXT_PUBLIC_APPWRITE_API_ENDPOINT}/storage/buckets/${process.env.NEXT_PUBLIC_APPWRITE_BLOG_IMAGES_BUCKET_ID}/files/${fileId}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`;
  } catch (error) {
    console.error('Error constructing image URL:', error);
    return '';
  }
}